//! # 隧道连接器模块
//!
//! 本模块实现了隧道连接的核心功能，包括：
//! - WebSocket 连接管理
//! - 数据包加密/解密处理
//! - 心跳检测和延迟监控
//! - 连接状态管理
//! - 流量统计

use crate::mapper::tunnel_mapper_context::TunnelMapperContext;
use crate::port_forward::port_forward::PortForward;
use crate::tunnel::tunnel::TunnelStatus;
use flyshadow_common::interface::interface_selector::InterfaceSelector;
use flyshadow_common::tunnel::package_encryption::{decrypt_package, encrypt_package};
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use flyshadow_common::util::dns_util::DnsUtil;
use futures_util::future::{select_ok, BoxFuture};
use futures_util::stream::{SplitSink, SplitStream};
use futures_util::{FutureExt, SinkExt, StreamExt};
use log::{debug, error, info, warn};
use socket2::SockRef;
use std::collections::HashMap;
use std::io::Error;
use std::net::{SocketAddr, ToSocketAddrs};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::net::{TcpSocket, TcpStream};
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::sleep;
use tokio_tungstenite::tungstenite::Message;
use tokio_tungstenite::WebSocketStream;
use tungstenite::Bytes;

// ==================== 常量定义 ====================

/// 心跳检测间隔时间（秒）
const PING_INTERVAL_SECONDS: u64 = 50;

/// 连接重试间隔时间（秒）
const RECONNECT_INTERVAL_SECONDS: u64 = 5;

/// 默认 WebSocket 主机
const DEFAULT_WS_HOST: &str = "www.baidu.com";

// ==================== 类型别名 ====================

/// WebSocket 写入器类型别名
type WsWriter = SplitSink<WebSocketStream<TcpStream>, Message>;

/// WebSocket 读取器类型别名
type WsReader = SplitStream<WebSocketStream<TcpStream>>;

// ==================== 主要结构体 ====================

/// 隧道连接器
///
/// 负责管理与隧道服务器的 WebSocket 连接，处理数据包的加密传输，
/// 维护连接状态和流量统计信息。
pub struct TunnelConnector {
    /// MD5 加密后的密码，用于数据包加密
    password_md5: RwLock<String>,

    /// 上传流量统计（字节）
    upload: Arc<RwLock<i64>>,

    /// 下载流量统计（字节）
    download: Arc<RwLock<i64>>,

    /// 隧道连接状态
    status: Arc<RwLock<TunnelStatus>>,

    /// 最后一次发送 PING 的时间戳（毫秒）
    ping_time: Arc<RwLock<u128>>,

    /// PING 延迟时间（毫秒）
    ping_delay: Arc<RwLock<u128>>,

    /// WebSocket 写入器，用于发送数据到服务器
    tcp_writer: Arc<RwLock<Option<WsWriter>>>,

    /// 数据读取任务句柄
    reader_job: Arc<RwLock<Option<JoinHandle<()>>>>,

    /// 心跳检测任务句柄
    ping_job: Arc<RwLock<Option<JoinHandle<()>>>>,

    /// 连接管理任务句柄
    connect_job: RwLock<Option<JoinHandle<()>>>,

    /// 隧道映射上下文，管理客户端连接
    tunnel_mapper_context: Arc<TunnelMapperContext>,

    /// 隧道连接器映射表
    tunnel_connector_map: Arc<RwLock<HashMap<String, Arc<TunnelConnector>>>>,

    /// 隧道唯一标识符
    tunnel_uuid: String,

    /// DNS 解析工具
    dns_util: Arc<DnsUtil>,

    /// 端口转发处理器
    port_forward: Arc<PortForward>,
}

// ==================== 私有方法实现 ====================

impl TunnelConnector {
    /// 发送 PING 命令到隧道服务器
    ///
    /// # 参数
    /// * `pwd` - 加密密码字节数组
    /// * `ping_time` - PING 时间戳存储
    /// * `writer` - WebSocket 写入器
    /// * `upload` - 上传流量统计
    /// * `tunnel_uuid` - 隧道唯一标识符（可选）
    ///
    /// # 返回值
    /// * `Ok(())` - 发送成功
    /// * `Err(String)` - 发送失败，包含错误信息
    async fn send_ping(
        pwd: &[u8],
        ping_time: Arc<RwLock<u128>>,
        writer: Arc<RwLock<Option<WsWriter>>>,
        upload: Arc<RwLock<i64>>,
        tunnel_uuid: Option<String>,
    ) -> Result<(), String> {
        // 记录 PING 发送时间
        *ping_time.write().await = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|e| format!("获取系统时间失败: {}", e))?
            .as_millis();

        // 构建 PING 数据包
        let data = tunnel_uuid.map(|uuid| uuid.into_bytes());
        let package = TunnelPackage::new(
            PackageCmd::PING,
            PackageProtocol::TCP,
            None,
            None,
            data,
        );

        Self::write_to_tunnel0(pwd, writer, upload, package).await
    }

    /// 发送登录请求到隧道服务器
    ///
    /// # 参数
    /// * `password_md5` - MD5 加密后的密码
    /// * `writer` - WebSocket 写入器
    /// * `upload` - 上传流量统计
    ///
    /// # 返回值
    /// * `Ok(())` - 登录请求发送成功
    /// * `Err(String)` - 发送失败，包含错误信息
    async fn login_tunnel(
        password_md5: String,
        writer: Arc<RwLock<Option<WsWriter>>>,
        upload: Arc<RwLock<i64>>,
    ) -> Result<(), String> {
        let package = TunnelPackage::new(
            PackageCmd::Login,
            PackageProtocol::TCP,
            None,
            None,
            Some(password_md5.clone().into_bytes()),
        );

        Self::write_to_tunnel0(password_md5.as_bytes(), writer, upload, package).await
    }

    /// 启动数据读取任务
    ///
    /// 创建一个异步任务来持续读取来自隧道服务器的数据包，
    /// 并根据包类型进行相应的处理。
    ///
    /// # 参数
    /// * `remote_addr` - 远程服务器地址
    /// * `password_md5` - MD5 加密后的密码
    /// * `tcp_reader` - WebSocket 读取器
    async fn start_reader_job(
        self: Arc<Self>,
        remote_addr: SocketAddr,
        password_md5: String,
        mut tcp_reader: WsReader,
    ) {
        let tunnel_connector = self.clone();

        // 启动数据读取任务
        *tunnel_connector.reader_job.write().await = Some(spawn(async move {
            info!("隧道数据读取任务已启动，远程地址: {}", remote_addr);

            // 持续读取 WebSocket 消息
            while let Some(result) = tcp_reader.next().await {
                match result {
                    Ok(message) => {
                        // 处理二进制消息（数据包）
                        if message.is_binary() {
                            let byte_arr = message.into_data();

                            // 更新下载流量统计
                            *self.download.write().await += byte_arr.len() as i64;

                            // 解密数据包
                            match decrypt_package(&byte_arr, password_md5.as_bytes()) {
                                Ok(Some(tunnel_package)) => {
                                    // 处理解密后的数据包
                                    self.handle_tunnel_package(tunnel_package, remote_addr, &password_md5).await;
                                }
                                Ok(None) => {
                                    warn!("接收到空数据包");
                                    break;
                                }
                                Err(e) => {
                                    error!("数据包解密失败: {}", e);
                                    break;
                                }
                            }
                        } else if message.is_close() {
                            info!("接收到 WebSocket 关闭消息");
                            break;
                        }
                    }
                    Err(e) => {
                        error!("WebSocket 连接错误: {:?}", e);
                        break;
                    }
                }
            }

            // 读取任务结束，设置状态为登出
            *self.status.write().await = TunnelStatus::Logout;
            info!("隧道数据读取任务已结束");
        }));
    }

    /// 处理隧道数据包
    ///
    /// 根据数据包的命令类型执行相应的处理逻辑。
    ///
    /// # 参数
    /// * `tunnel_package` - 隧道数据包
    /// * `remote_addr` - 远程服务器地址
    /// * `password_md5` - MD5 加密后的密码
    async fn handle_tunnel_package(
        &self,
        tunnel_package: TunnelPackage,
        remote_addr: SocketAddr,
        password_md5: &str,
    ) {
        match tunnel_package.cmd {
            // 登录相关命令（服务器端不应发送，忽略）
            PackageCmd::Login | PackageCmd::NewConnect => {
                debug!("忽略服务器发送的登录/新连接命令");
            }

            // 关闭连接命令
            PackageCmd::CloseConnect => {
                self.handle_close_connect(&tunnel_package).await;
            }

            // 传输数据命令
            PackageCmd::TData => {
                self.handle_tdata(tunnel_package, remote_addr, password_md5).await;
            }

            // PING 命令（服务器端不应发送，忽略）
            PackageCmd::PING => {
                debug!("忽略服务器发送的 PING 命令");
            }

            // 登录成功响应
            PackageCmd::LoginSuccess => {
                info!("隧道登录成功");
                *self.status.write().await = TunnelStatus::Success;
            }

            // 登录失败响应
            PackageCmd::LoginFail => {
                error!("隧道登录失败");
                *self.status.write().await = TunnelStatus::Logout;
            }

            // 协议错误响应
            PackageCmd::ProtocolError => {
                error!("隧道协议错误");
                *self.status.write().await = TunnelStatus::Logout;
            }

            // PONG 响应（心跳回复）
            PackageCmd::PONG => {
                self.handle_pong().await;
            }

            // 客户端新连接命令
            PackageCmd::CNewConnect => {
                self.port_forward.clone().new_connect(tunnel_package).await;
            }

            // 客户端关闭连接命令
            PackageCmd::CCloseConnect => {
                self.port_forward.close_connect(tunnel_package).await;
            }

            // 客户端传输数据命令
            PackageCmd::CTdata => {
                self.port_forward.clone().transmit_data(tunnel_package).await;
            }

            // 无效命令
            PackageCmd::NONE => {
                warn!("接收到无效的数据包命令");
            }
        }
    }

    /// 处理关闭连接命令
    async fn handle_close_connect(&self, tunnel_package: &TunnelPackage) {
        if let Some(source_addr) = &tunnel_package.source_address {
            let key = format!("{}==={:?}", source_addr, tunnel_package.protocol);
            self.tunnel_mapper_context.close(&key).await;
            self.tunnel_connector_map.write().await.remove(source_addr);
            debug!("关闭连接: {}", key);
        }
    }

    /// 处理传输数据命令
    async fn handle_tdata(
        &self,
        mut tunnel_package: TunnelPackage,
        remote_addr: SocketAddr,
        password_md5: &str,
    ) {
        let protocol = tunnel_package.protocol;

        // 检查是否有目标地址
        let Some(target_address) = tunnel_package.target_address.clone() else {
            warn!("传输数据包缺少目标地址");
            return;
        };

        // 如果是原生 UDP，需要拼接服务器 IP 和端口
        if protocol == PackageProtocol::NativeUdp {
            tunnel_package.target_address = Some(format!("{}:{}", remote_addr.ip(), target_address));
        }

        // 检查是否有源地址
        let Some(source_addr) = tunnel_package.source_address.clone() else {
            warn!("传输数据包缺少源地址");
            return;
        };

        let key = format!("{}==={:?}", source_addr, protocol);

        // 尝试写入客户端
        if !self.tunnel_mapper_context.write_to_client(&key, tunnel_package).await {
            // 写入失败，如果是 TCP 连接则发送关闭连接命令
            if protocol == PackageProtocol::TCP {
                let close_package = TunnelPackage {
                    cmd: PackageCmd::CloseConnect,
                    protocol: PackageProtocol::TCP,
                    source_address: Some(source_addr),
                    target_address: Some(target_address),
                    data: None,
                };

                if let Err(e) = Self::write_to_tunnel0(
                    password_md5.as_bytes(),
                    self.tcp_writer.clone(),
                    self.upload.clone(),
                    close_package,
                ).await {
                    error!("发送关闭连接命令失败: {}", e);
                }
            }
        }
    }

    /// 处理 PONG 响应（心跳回复）
    async fn handle_pong(&self) {
        let ping_time = *self.ping_time.read().await;
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis();

        let delay = current_time.saturating_sub(ping_time);
        *self.ping_delay.write().await = delay;

        info!("隧道延迟: {}ms", delay);
    }

    /// 写入数据包到隧道连接
    ///
    /// 将数据包序列化、加密后通过 WebSocket 发送到隧道服务器。
    ///
    /// # 参数
    /// * `pwd` - 加密密码字节数组
    /// * `writer` - WebSocket 写入器
    /// * `upload` - 上传流量统计
    /// * `tunnel_package` - 要发送的隧道数据包
    ///
    /// # 返回值
    /// * `Ok(())` - 发送成功
    /// * `Err(String)` - 发送失败，包含错误信息
    async fn write_to_tunnel0(
        pwd: &[u8],
        writer: Arc<RwLock<Option<WsWriter>>>,
        upload: Arc<RwLock<i64>>,
        tunnel_package: TunnelPackage,
    ) -> Result<(), String> {
        // 检查写入器是否可用
        if writer.read().await.is_none() {
            return Err("隧道写入器不可用".to_string());
        }

        // 序列化数据包
        let mut packet_bytes = Vec::new();
        tunnel_package.to_byte_array(&mut packet_bytes);

        // 加密数据包
        let encrypted_data = encrypt_package(pwd, &packet_bytes)
            .map_err(|e| format!("数据包加密失败: {}", e))?;

        let data_len = encrypted_data.len() as i64;

        // 发送加密后的数据包 - 缩小锁的作用域
        {
            let mut writer_guard = writer.write().await;
            let Some(ws_writer) = writer_guard.as_mut() else {
                return Err("隧道写入器不可用".to_string());
            };

            // 发送二进制消息
            ws_writer
                .send(Message::Binary(Bytes::from(encrypted_data)))
                .await
                .map_err(|e| {
                    error!("隧道数据发送失败: {}", e);
                    format!("隧道数据发送失败: {}", e)
                })?;
        } // writer_guard 在这里被释放

        // 在锁外更新上传流量统计，避免在持有 writer 锁时获取 upload 锁
        *upload.write().await += data_len;

        Ok(())
    }
}

// ==================== 公共方法实现 ====================

impl TunnelConnector {
    /// 创建新的隧道连接器实例
    ///
    /// # 参数
    /// * `tunnel_uuid` - 隧道唯一标识符
    /// * `tunnel_mapper_context` - 隧道映射上下文
    /// * `tunnel_connector_map` - 隧道连接器映射表
    /// * `port_forward` - 端口转发处理器
    ///
    /// # 返回值
    /// 返回新创建的 `TunnelConnector` 实例
    pub fn new(
        tunnel_uuid: String,
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_connector_map: Arc<RwLock<HashMap<String, Arc<TunnelConnector>>>>,
        port_forward: Arc<PortForward>,
    ) -> TunnelConnector {
        TunnelConnector {
            password_md5: RwLock::new(String::new()),
            upload: Arc::new(RwLock::new(0)),
            download: Arc::new(RwLock::new(0)),
            status: Arc::new(RwLock::new(TunnelStatus::WaitLogin)),
            ping_time: Arc::new(RwLock::new(0)),
            ping_delay: Arc::new(RwLock::new(0)),
            tcp_writer: Arc::new(RwLock::new(None)),
            reader_job: Arc::new(RwLock::new(None)),
            ping_job: Arc::new(RwLock::new(None)),
            connect_job: RwLock::new(None),
            tunnel_mapper_context,
            tunnel_connector_map,
            tunnel_uuid,
            dns_util: Arc::new(DnsUtil::new(false)),
            port_forward,
        }
    }

    /// 连接到隧道服务器
    ///
    /// 启动隧道连接，包括自动重连机制。如果连接断开，
    /// 会自动尝试重新连接。
    ///
    /// # 参数
    /// * `host` - 服务器主机地址（支持多节点格式）
    /// * `port` - 服务器端口号
    /// * `password` - 连接密码
    pub async fn connect(self: Arc<Self>, host: String, port: u16, password: String) {
        info!("开始连接隧道服务器: {}:{}", host, port);

        // 先断开现有连接
        self.disconnect().await;

        let tunnel_connector = self.clone();

        // 缩小锁的作用域，避免在持有锁时创建任务
        let old_job = {
            let mut connect_job = self.connect_job.write().await;
            connect_job.take()
        };

        // 在锁外中止旧任务
        if let Some(job_handler) = old_job {
            job_handler.abort();
        }

        // 创建新任务
        let new_job = spawn(async move {
            let status_clone = tunnel_connector.status.clone();

            // 首次连接尝试
            tunnel_connector.clone().connect_internal(host.clone(), port, password.clone()).await;

            // 自动重连循环
            loop {
                sleep(Duration::from_secs(RECONNECT_INTERVAL_SECONDS)).await;

                if *status_clone.read().await == TunnelStatus::Logout {
                    info!("检测到连接断开，尝试重新连接...");
                    tunnel_connector.clone().connect_internal(host.clone(), port, password.clone()).await;
                }
            }
        });

        // 最后设置新任务
        *self.connect_job.write().await = Some(new_job);
    }

    /// 内部连接实现
    ///
    /// 执行实际的隧道连接逻辑，包括 DNS 解析、TCP 连接建立、
    /// WebSocket 握手、登录验证等步骤。
    ///
    /// # 参数
    /// * `host` - 服务器主机地址
    /// * `port` - 服务器端口号
    /// * `password` - 连接密码
    async fn connect_internal(self: Arc<Self>, host: String, port: u16, password: String) {
        // 生成 MD5 加密密码
        let md5_pwd = md5::compute(password.as_bytes());
        let password_md5 = format!("{:x}", md5_pwd);
        *self.password_md5.write().await = password_md5.clone();

        // 解析主机地址和 WebSocket 主机
        let (resolved_host, ws_host) = self.parse_host_config(&host);

        // 设置连接状态为等待登录
        *self.status.write().await = TunnelStatus::WaitLogin;

        // 停止之前的任务
        self.stop_background_tasks().await;

        // DNS 解析
        let ip_addresses = match self.resolve_host_addresses(&resolved_host).await {
            Ok(addrs) => addrs,
            Err(e) => {
                error!("DNS 解析失败 {}: {}", resolved_host, e);
                *self.status.write().await = TunnelStatus::Logout;
                return;
            }
        };

        // 建立 TCP 连接
        let tcp_stream = match self.establish_tcp_connection(&ip_addresses, port).await {
            Ok(stream) => stream,
            Err(e) => {
                error!("TCP 连接失败 {:?}:{}: {}", ip_addresses, port, e);
                *self.status.write().await = TunnelStatus::Logout;
                return;
            }
        };

        // 配置 TCP 连接参数
        self.configure_tcp_stream(&tcp_stream);

        // 获取远程地址
        let remote_addr = match tcp_stream.peer_addr() {
            Ok(addr) => addr,
            Err(e) => {
                error!("获取远程地址失败: {}", e);
                *self.status.write().await = TunnelStatus::Logout;
                return;
            }
        };

        // 建立 WebSocket 连接
        let (ws_sender, ws_receiver) = match self.establish_websocket_connection(
            tcp_stream,
            &ws_host,
            &password_md5,
        ).await {
            Ok((sender, receiver)) => (sender, receiver),
            Err(e) => {
                error!("WebSocket 握手失败: {}", e);
                *self.status.write().await = TunnelStatus::Logout;
                return;
            }
        };

        *self.tcp_writer.write().await = Some(ws_sender);

        // 执行登录和初始化
        if let Err(e) = self.perform_login_and_initialization(&password_md5).await {
            error!("登录和初始化失败: {}", e);
            *self.status.write().await = TunnelStatus::Logout;
            return;
        }

        // 启动心跳任务
        self.start_ping_task(password_md5.clone()).await;

        // 启动数据读取任务
        self.clone().start_reader_job(remote_addr, password_md5, ws_receiver).await;

        info!("隧道连接建立成功，远程地址: {}", remote_addr);
    }

    /// 解析主机配置
    ///
    /// 从主机字符串中解析出实际的主机地址和 WebSocket 主机。
    ///
    /// # 参数
    /// * `host` - 主机配置字符串
    ///
    /// # 返回值
    /// 返回 (实际主机地址, WebSocket主机) 元组
    fn parse_host_config(&self, host: &str) -> (String, String) {
        let host_parts: Vec<String> = host
            .split(':')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();

        if host_parts.len() >= 2 {
            (host_parts[0].clone(), host_parts[1..].join(":"))
        } else {
            (host_parts[0].clone(), DEFAULT_WS_HOST.to_string())
        }
    }

    /// 停止后台任务
    async fn stop_background_tasks(&self) {
        // 收集需要停止的任务，避免在持有锁时调用 abort()
        let reader_job = self.reader_job.write().await.take();
        let ping_job = self.ping_job.write().await.take();

        // 在锁外停止任务，避免死锁
        if let Some(job) = reader_job {
            job.abort();
        }
        if let Some(job) = ping_job {
            job.abort();
        }
    }

    /// 解析主机地址
    async fn resolve_host_addresses(&self, host: &str) -> Result<Vec<std::net::IpAddr>, String> {
        let interface_selector = if self.tunnel_mapper_context.get_tun_mode_enable().await {
            Some(self.tunnel_mapper_context.get_local_interface_selector().await)
        } else {
            None
        };

        self.dns_util
            .query_ip(interface_selector, &host.to_string())
            .await
            .map_err(|e| format!("DNS 解析失败: {}", e))
    }

    /// 建立 TCP 连接
    ///
    /// 尝试连接到多个 IP 地址，支持 IPv4 和 IPv6，
    /// 使用并发连接策略提高连接成功率。
    async fn establish_tcp_connection(
        &self,
        ip_addresses: &[std::net::IpAddr],
        port: u16,
    ) -> Result<TcpStream, String> {
        info!("开始连接服务器 {:?}:{}", ip_addresses, port);

        let interface_selector = if self.tunnel_mapper_context.get_tun_mode_enable().await {
            Some(self.tunnel_mapper_context.get_local_interface_selector().await)
        } else {
            None
        };

        let mut connect_futures: Vec<BoxFuture<Result<TcpStream, Error>>> = Vec::new();
        let mut first_ipv4 = true;
        let mut first_ipv6 = true;

        // 为每个 IP 地址创建连接 Future
        for &ip_addr in ip_addresses {
            let interface_selector = interface_selector.clone();

            if ip_addr.is_ipv4() {
                let connect_fn = async move {
                    let tcp_socket = TcpSocket::new_v4()?;
                    if let Some(interface_selector) = interface_selector {
                        InterfaceSelector::tcp_bind_ipv4(interface_selector, &tcp_socket)?;
                    }
                    tcp_socket.connect((ip_addr, port).to_socket_addrs()?.next().unwrap()).await
                };

                if first_ipv4 {
                    connect_futures.push(connect_fn.boxed());
                    first_ipv4 = false;
                } else {
                    // 为后续的 IPv4 地址添加延迟，避免同时连接
                    connect_futures.push(async move {
                        sleep(Duration::from_secs(1)).await;
                        connect_fn.await
                    }.boxed());
                }
            } else if ip_addr.is_ipv6() {
                let connect_fn = async move {
                    let tcp_socket = TcpSocket::new_v6()?;
                    if let Some(interface_selector) = interface_selector {
                        InterfaceSelector::tcp_bind_ipv6(interface_selector, &tcp_socket)?;
                    }
                    tcp_socket.connect((ip_addr, port).to_socket_addrs()?.next().unwrap()).await
                };

                if first_ipv6 {
                    connect_futures.push(connect_fn.boxed());
                    first_ipv6 = false;
                } else {
                    // 为后续的 IPv6 地址添加延迟，避免同时连接
                    connect_futures.push(async move {
                        sleep(Duration::from_secs(1)).await;
                        connect_fn.await
                    }.boxed());
                }
            }
        }

        // 并发尝试连接，返回第一个成功的连接
        let (tcp_stream, _) = select_ok(connect_futures)
            .await
            .map_err(|e| format!("所有连接尝试都失败: {}", e))?;

        info!("TCP 连接建立成功: {:?}", tcp_stream.peer_addr());
        Ok(tcp_stream)
    }

    /// 配置 TCP 连接参数
    ///
    /// 设置 TCP 连接的各种参数以优化性能。
    fn configure_tcp_stream(&self, tcp_stream: &TcpStream) {
        // 禁用 Nagle 算法，减少延迟
        if let Err(e) = tcp_stream.set_nodelay(true) {
            warn!("设置 TCP_NODELAY 失败: {}", e);
        }

        // 启用 TCP Keep-Alive
        let sock_ref = SockRef::from(tcp_stream);
        if let Err(e) = sock_ref.set_keepalive(true) {
            warn!("设置 TCP Keep-Alive 失败: {}", e);
        }
    }

    /// 建立 WebSocket 连接
    ///
    /// 在已建立的 TCP 连接上执行 WebSocket 握手。
    async fn establish_websocket_connection(
        &self,
        tcp_stream: TcpStream,
        ws_host: &str,
        password_md5: &str,
    ) -> Result<(WsWriter, WsReader), String> {
        let url = format!("ws://{}/p/{}", ws_host, password_md5);
        debug!("WebSocket 连接 URL: {}", url);

        let (ws_stream, _) = tokio_tungstenite::client_async(url, tcp_stream)
            .await
            .map_err(|e| format!("WebSocket 握手失败: {}", e))?;

        let (ws_sender, ws_receiver) = ws_stream.split();
        Ok((ws_sender, ws_receiver))
    }

    /// 执行登录和初始化
    ///
    /// 发送登录请求和初始 PING 命令。
    async fn perform_login_and_initialization(&self, password_md5: &str) -> Result<(), String> {
        // 发送登录请求
        Self::login_tunnel(
            password_md5.to_string(),
            self.tcp_writer.clone(),
            self.upload.clone(),
        ).await?;

        // 发送初始 PING（带隧道 UUID）
        Self::send_ping(
            password_md5.as_bytes(),
            self.ping_time.clone(),
            self.tcp_writer.clone(),
            self.upload.clone(),
            Some(self.tunnel_uuid.clone()),
        ).await?;

        Ok(())
    }

    /// 启动心跳检测任务
    ///
    /// 定期发送 PING 命令以维持连接活跃状态。
    async fn start_ping_task(&self, password_md5: String) {
        let ping_time = self.ping_time.clone();
        let tcp_writer = self.tcp_writer.clone();
        let upload = self.upload.clone();
        let status = self.status.clone();

        *self.ping_job.write().await = Some(spawn(async move {
            info!("心跳检测任务已启动");

            loop {
                sleep(Duration::from_secs(PING_INTERVAL_SECONDS)).await;

                // 发送心跳 PING
                if let Err(e) = Self::send_ping(
                    password_md5.as_bytes(),
                    ping_time.clone(),
                    tcp_writer.clone(),
                    upload.clone(),
                    None,
                ).await {
                    error!("心跳检测失败: {}", e);
                    *status.write().await = TunnelStatus::Logout;
                    break;
                }
            }

            info!("心跳检测任务已结束");
        }));
    }

    /// 获取并重置上传流量统计
    ///
    /// 返回当前的上传流量字节数，并将计数器重置为 0。
    ///
    /// # 返回值
    /// 返回上传的字节数
    pub async fn get_upload(&self) -> i64 {
        let mut upload_guard = self.upload.write().await;
        let current_upload = *upload_guard;
        *upload_guard = 0;
        current_upload
    }

    /// 获取并重置下载流量统计
    ///
    /// 返回当前的下载流量字节数，并将计数器重置为 0。
    ///
    /// # 返回值
    /// 返回下载的字节数
    pub async fn get_download(&self) -> i64 {
        let mut download_guard = self.download.write().await;
        let current_download = *download_guard;
        *download_guard = 0;
        current_download
    }

    /// 获取隧道连接状态
    ///
    /// # 返回值
    /// 返回当前的隧道连接状态
    pub async fn get_status(&self) -> TunnelStatus {
        *self.status.read().await
    }

    /// 获取 PING 延迟时间
    ///
    /// # 返回值
    /// 返回最近一次 PING 的延迟时间（毫秒）
    pub async fn get_ping_delay(&self) -> u128 {
        *self.ping_delay.read().await
    }

    /// 断开隧道连接
    ///
    /// 停止所有后台任务，关闭 WebSocket 连接，
    /// 并重置连接状态。
    pub async fn disconnect(&self) {
        info!("开始断开隧道连接");

        // 设置连接状态为等待登录
        *self.status.write().await = TunnelStatus::WaitLogin;

        // 关闭 WebSocket 写入器
        *self.tcp_writer.write().await = None;

        // 收集需要停止的任务，避免在持有锁时调用 abort()
        let reader_job = self.reader_job.write().await.take();
        let ping_job = self.ping_job.write().await.take();
        let connect_job = self.connect_job.write().await.take();

        // 在锁外停止任务，避免死锁
        if let Some(job) = reader_job {
            job.abort();
            debug!("数据读取任务已停止");
        }

        if let Some(job) = ping_job {
            job.abort();
            debug!("心跳检测任务已停止");
        }

        if let Some(job) = connect_job {
            job.abort();
            debug!("连接管理任务已停止");
        }

        info!("隧道连接已断开");
    }

    /// 写入数据包到隧道连接
    ///
    /// 检查连接状态后，将数据包发送到隧道服务器。
    /// 如果发送失败，会自动设置连接状态为登出。
    ///
    /// # 参数
    /// * `tunnel_package` - 要发送的隧道数据包
    ///
    /// # 返回值
    /// * `Ok(())` - 发送成功
    /// * `Err(String)` - 发送失败，包含错误信息
    pub async fn write_to_tunnel(&self, tunnel_package: TunnelPackage) -> Result<(), String> {
        // 检查连接状态
        let status = *self.status.read().await;
        if status != TunnelStatus::Success {
            return Err("隧道连接未建立".to_string());
        }

        // 获取密码字节数组
        let password_bytes = self.password_md5.read().await.clone().into_bytes();

        // 发送数据包
        let result = Self::write_to_tunnel0(
            &password_bytes,
            self.tcp_writer.clone(),
            self.upload.clone(),
            tunnel_package,
        ).await;

        // 如果发送失败，设置状态为登出
        if result.is_err() {
            *self.status.write().await = TunnelStatus::Logout;
        }

        result
    }
}
